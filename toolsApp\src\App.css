* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background: #1a1a1a;
  color: #ffffff;
  overflow: hidden;
}

.app {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.animation-editor {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 340px 1fr 340px;
  grid-template-rows: 1fr auto;
  grid-template-areas:
    "left-panel canvas right-panel"
    "timeline timeline timeline";
}

.left-panel {
  grid-area: left-panel;
  background: #2a2a2a;
  border-right: 1px solid #444;
  padding: 15px;
  overflow-y: auto;
}

.canvas-container {
  grid-area: canvas;
  background: #333;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.canvas-container canvas {
  border: 2px solid #333;
  background: #9aa0a6; /* нейтральный серый фон для удобства */
}

.right-panel {
  grid-area: right-panel;
  background: #2a2a2a;
  border-left: 1px solid #444;
  padding: 15px;
  overflow-y: auto;
}

.timeline {
  grid-area: timeline;
  background: #222;
  border-top: 1px solid #444;
  padding: 15px;
  height: 100px;  /* Увеличиваю высоту */
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
  bottom: 0;
}

.btn {
  background: #4a4a4a;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.btn:hover {
  background: #5a5a5a;
}

.btn:active {
  background: #3a3a3a;
}

.btn.primary {
  background: #007acc;
}

.btn.primary:hover {
  background: #0088dd;
}

.panel-section {
  margin-bottom: 20px;
}

.panel-section h3 {
  margin-bottom: 10px;
  color: #ccc;
  font-size: 14px;
  text-transform: uppercase;
}

.control-group {
  margin-bottom: 10px;
}

.control-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  color: #aaa;
}

.control-group input,
.control-group select {
  width: 100%;
  padding: 6px;
  background: #1a1a1a;
  border: 1px solid #555;
  border-radius: 3px;
  color: white;
  font-size: 12px;
}

.timeline-track {
  flex: 1;
  height: 50px;  /* Увеличиваю высоту трека */
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.timeline-frames {
  display: flex;
  height: 100%;
  align-items: center;
  padding: 0 10px;
  gap: 8px;
}

.frame-dot {
  width: 16px;  /* Увеличиваю размер */
  height: 16px;
  border-radius: 50%;
  background: #666;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
}

.frame-dot.active {
  background: #007acc;
  border-color: #ffffff;
  transform: scale(1.2);
}

.frame-dot:hover {
  background: #888;
}

.playback-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.file-input {
  display: none;
}

.file-input-label {
  background: #4a4a4a;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
  display: inline-block;
}

.file-input-label:hover {
  background: #5a5a5a;
}
