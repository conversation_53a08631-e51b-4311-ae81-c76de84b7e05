import { useState } from 'react';
import type { AnimationFrame } from '../types/skeleton';

interface TimelineProps {
  frames: AnimationFrame[];
  currentFrameIndex: number;
  isPlaying: boolean;
  isLooping: boolean;
  onFrameSelect: (frameIndex: number) => void;
  onPlay: () => void;
  onPause: () => void;
  onStop: () => void;
  onLoopToggle: () => void;
  onAddFrame: () => void;
  onDuplicateFrame: () => void;
  onDeleteFrame: () => void;
  onReorderFrames: (fromIndex: number, toIndex: number) => void;
}

const Timeline = ({
  frames,
  currentFrameIndex,
  isPlaying,
  isLooping,
  onFrameSelect,
  onPlay,
  onPause,
  onStop,
  onLoopToggle,
  onAddFrame,
  onDuplicateFrame,
  onDeleteFrame,
  onReorderFrames
}: TimelineProps) => {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);

  const handleDragStart = (index: number, e: React.DragEvent) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (index: number, e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (toIndex: number, e: React.DragEvent) => {
    e.preventDefault();
    setDragOverIndex(null);

    if (draggedIndex !== null && draggedIndex !== toIndex) {
      onReorderFrames(draggedIndex, toIndex);
    }
    setDraggedIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  return (
    <div className="timeline">
      <div className="playback-controls">
        <button className="btn" onClick={onPlay} disabled={isPlaying}>
          ▶️ Play
        </button>
        <button className="btn" onClick={onPause} disabled={!isPlaying}>
          ⏸️ Pause
        </button>
        <button className="btn" onClick={onStop}>
          ⏹️ Stop
        </button>
        <button 
          className={`btn ${isLooping ? 'primary' : ''}`} 
          onClick={onLoopToggle}
        >
          🔄 Loop
        </button>
      </div>

      <div className="frame-controls" style={{ display: 'flex', gap: '8px', marginBottom: '8px' }}>
        <button className="btn" onClick={onAddFrame}>
          ➕ Новый кадр
        </button>
        <button 
          className="btn" 
          onClick={onDuplicateFrame}
          disabled={currentFrameIndex === -1 || frames.length === 0}
          title={currentFrameIndex === -1 ? "Выберите кадр для дублирования" : "Дублировать текущий кадр"}
        >
          📋 Дублировать кадр
        </button>
        <button 
          className="btn" 
          onClick={onDeleteFrame}
          disabled={currentFrameIndex === -1 || frames.length === 0}
          title={currentFrameIndex === -1 ? "Выберите кадр для удаления" : "Удалить текущий кадр"}
          style={{ background: '#b83232' }}
        >
          🗑️ Удалить кадр
        </button>
      </div>

      <div className="timeline-track">
        <div className="timeline-frames">
          {frames.length === 0 ? (
            <div style={{ color: '#666', fontSize: '12px' }}>
              Нет кадров - создайте первый кадр
            </div>
          ) : (
            frames.map((_, index) => {
              const isDragging = draggedIndex === index;
              const isDragOver = dragOverIndex === index;
              
              return (
                <div
                  key={index}
                  className={`frame-dot ${index === currentFrameIndex ? 'active' : ''}`}
                  draggable
                  onDragStart={(e) => handleDragStart(index, e)}
                  onDragOver={(e) => handleDragOver(index, e)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(index, e)}
                  onDragEnd={handleDragEnd}
                  onClick={() => onFrameSelect(index)}
                  title={`Кадр ${index + 1}`}
                  style={{
                    opacity: isDragging ? 0.5 : 1,
                    borderTop: isDragOver ? '3px solid #ffcc00' : undefined,
                    cursor: 'grab'
                  }}
                />
              );
            })
          )}
        </div>
      </div>

      <div style={{ color: '#aaa', fontSize: '12px', minWidth: '80px' }}>
        {frames.length > 0 && (
          <>Кадр {currentFrameIndex + 1}/{frames.length}</>
        )}
      </div>
    </div>
  );
};

export default Timeline;