import { useRef } from 'react';
import type { AnimationData } from './AnimationEditor';

interface LeftPanelProps {
  animation: AnimationData;
  onLoadAnimation: (animation: AnimationData) => void;
  onSaveAnimation: () => void;
  playbackSpeed: number;
  onPlaybackSpeedChange: (speed: number) => void;
  onAnimationNameChange: (name: string) => void;
  zoom: number;
  onZoomChange: (zoom: number) => void;
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  showBoneNames: boolean;
  onShowBoneNamesChange: (show: boolean) => void;
}

const LeftPanel = ({
  animation,
  onLoadAnimation,
  onSaveAnimation,
  playbackSpeed,
  onPlaybackSpeedChange,
  onAnimationNameChange,
  zoom,
  onZoomChange,
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  showBoneNames,
  onShowBoneNamesChange
}: LeftPanelProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileLoad = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        const text = event.target?.result as string;
        const jsonData = JSON.parse(text);
        
        let animationData: AnimationData;
        
        // Проверяем формат файла
        if (jsonData.name && jsonData.frames) {
          // Наш формат: {name: "...", frames: [...]}
          animationData = jsonData as AnimationData;
        } else {
          // Формат игры: {"animation_name": [...]}
          const animationName = Object.keys(jsonData)[0];
          const frames = jsonData[animationName];
          
          if (animationName && Array.isArray(frames)) {
            animationData = {
              name: animationName,
              frames: frames
            };
          } else {
            throw new Error('Неизвестный формат файла');
          }
        }
        
        onLoadAnimation(animationData);
      } catch (error) {
        alert('Ошибка загрузки файла: ' + error);
      }
    };
    reader.readAsText(file);
  };

  const handleLoadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="left-panel">
      <div className="panel-section">
        <h3>Файл</h3>
        <button className="btn" onClick={handleLoadClick}>
          Загрузить анимацию
        </button>
        <input
          ref={fileInputRef}
          type="file"
          accept=".json"
          onChange={handleFileLoad}
          className="file-input"
        />
        <button className="btn" onClick={onSaveAnimation}>
          Скачать анимацию
        </button>
      </div>

      <div className="panel-section">
        <h3>История</h3>
        <div style={{ display: 'flex', gap: '8px' }}>
          <button 
            className="btn" 
            onClick={onUndo}
            disabled={!canUndo}
            title="Отменить (Ctrl+Z)"
          >
            ↶ Отменить
          </button>
          <button 
            className="btn" 
            onClick={onRedo}
            disabled={!canRedo}
            title="Повторить (Ctrl+Y)"
          >
            ↷ Повторить
          </button>
        </div>
      </div>

      <div className="panel-section">
        <h3>Анимация</h3>
        <div className="control-group">
          <label>Название</label>
          <input
            type="text"
            value={animation.name}
            onChange={(e) => onAnimationNameChange(e.target.value)}
          />
        </div>
        <div className="control-group">
          <label>Кадров: {animation.frames.length}</label>
        </div>
      </div>

      <div className="panel-section">
        <h3>Воспроизведение</h3>
        <div className="control-group">
          <label>Скорость (мс/кадр)</label>
          <input
            type="range"
            min="50"
            max="1000"
            step="50"
            value={playbackSpeed}
            onChange={(e) => onPlaybackSpeedChange(Number(e.target.value))}
          />
          <span style={{ fontSize: '12px', color: '#aaa' }}>
            {playbackSpeed}мс
          </span>
        </div>
        
        <div className="control-group">
          <label>Зум</label>
          <input
            type="range"
            min="0.3"
            max="3"
            step="0.1"
            value={zoom}
            onChange={(e) => onZoomChange(Number(e.target.value))}
          />
          <span style={{ fontSize: '12px', color: '#aaa' }}>
            {Math.round(zoom * 100)}%
          </span>
        </div>

        <div className="control-group">
          <button 
            className="btn"
            onClick={() => onShowBoneNamesChange(!showBoneNames)}
            style={{ 
              backgroundColor: showBoneNames ? '#4CAF50' : '#666',
              padding: '8px 12px'
            }}
          >
            {showBoneNames ? '👁️ Скрыть названия' : '👁️‍🗨️ Показать названия'}
          </button>
        </div>
      </div>

      <div className="panel-section">
        <h3>Инструкции</h3>
        <div style={{ fontSize: '11px', color: '#999', lineHeight: '1.4' }}>
          <p>• Кликните по кости для выбора</p>
          <p>• Перетаскивайте для поворота</p>
          <p>• Используйте правую панель для точной настройки</p>
          <p>• Кнопка "Запечатлить" сохранит текущую позу</p>
        </div>
      </div>
    </div>
  );
};

export default LeftPanel;