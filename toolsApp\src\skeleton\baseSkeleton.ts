import type { Skeleton } from '../types/skeleton';

// Базовая структура скелета (позиция покоя)
// rotation - в радианах 90 градусов = 1.5708
export const baseSkeleton: Skeleton = {

  
  torso: { parent: null, x: 0, y: -140, rotation: 0, length: 70, width: 44 },
  head: { parent: 'torso', x: 0, y: -24, rotation: 0, length: 30, width: 28 },
  
  // РУКИ - симметричные значения
  leftShoulder: { parent: 'torso', x: -28, y: 5, rotation: 0, length: 36, width: 18 },
  leftForearm: { parent: 'leftShoulder', x: -3, y: 35, rotation: 0, length: 30, width: 13 },
  leftHand: { parent: 'leftForearm', x: 0, y: 30, rotation: 0, length: 15, width: 14 },
  rightShoulder: { parent: 'torso', x: 28, y: 5, rotation: 0, length: 36, width: 18 },
  rightForearm: { parent: 'rightShoulder', x: 3, y: 35, rotation: 0, length: 30, width: 13 },
  rightHand: { parent: 'rightForearm', x: 0, y: 30, rotation: 0, length: 15, width: 14 },
  
  // НОГИ - симметричные значения
  leftThigh: { parent: 'torso', x: -10, y: 65, rotation: 0.11, length: 40, width: 21 },
  leftShin: { parent: 'leftThigh', x: 0, y: 40, rotation: -0.11, length: 36, width: 14 },
  leftFoot: { parent: 'leftShin', x: -3, y: 35, rotation: 0, length: 12, width: 22 },
  rightThigh: { parent: 'torso', x: 10, y: 65, rotation: -0.11, length: 40, width: 21 },
  rightShin: { parent: 'rightThigh', x: -2, y: 40, rotation: 0.11, length: 36, width: 14 },
  rightFoot: { parent: 'rightShin', x: 3, y: 35, rotation: 0, length: 12, width: 22 },
  
  // ОРУЖИЕ (опционально для роботов/зомби может не быть)
  weapon: { parent: 'rightHand', x: 0, y: -20, rotation: 0, length: 40, width: 4 },
};