import { useRef, useEffect, useState } from 'react';
import type { Skeleton, WorldTransform } from '../types/skeleton';
import { baseSkeleton } from '../skeleton/baseSkeleton';

interface SkeletonCanvasProps {
  skeleton: Skeleton;
  onSkeletonChange: (skeleton: Skeleton) => void;
  zoom: number;
  sensitivity?: number; // 0..1, lower = less sensitive
  showBoneNames?: boolean;
  selectedBone?: string | null;
  onSelectBone?: (name: string | null) => void;
  drawOrder?: string[];
  previousSkeleton?: Skeleton | null;
  nextSkeleton?: Skeleton | null;
}

const SkeletonCanvas = ({ skeleton, onSkeletonChange, zoom, showBoneNames = true, selectedBone, onSelectBone, drawOrder, previousSkeleton, nextSkeleton }: SkeletonCanvasProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  // localSelectedBone mirrors selectedBone prop if provided
  const [localSelectedBone, setLocalSelectedBone] = useState<string | null>(selectedBone || null);
  const [showTextures, setShowTextures] = useState<boolean>(false);
  const [loadedTextures, setLoadedTextures] = useState<Map<string, HTMLImageElement>>(new Map());

  // Получить путь к текстуре для кости (упрощенная версия для toolsApp)
  const getTexturePathForBone = (boneName: string): string | null => {
    // Маппинг костей на индексы текстур (базовая мужская версия, front view)
    const textureMap: Record<string, number> = {
     head: 1,
  torso: 2,
  leftShoulder: 3,
  leftForearm: 4,
  leftHand: 5,
  rightShoulder: 6,
  rightForearm: 7,
  rightHand: 8,
  rightThigh: 9,
  rightShin: 10,
  rightFoot: 14,
  leftThigh: 12,
  leftShin: 13,
  leftFoot: 11,
  weapon: 15, // если есть
    };

    const textureIndex = textureMap[boneName];
    if (textureIndex === undefined) return null;

    // Путь к текстурам в toolsApp (упрощенная структура)
    const genderFolder = 'male';
    const viewFolder = 'front';
    
    // Для головы используем face1
    if (boneName === 'head') {
      return `/${genderFolder}/${viewFolder}/face1.png`;
    }
    
    // Для остальных частей тела
    return `/${genderFolder}/${viewFolder}/${textureIndex}.png`;
  };

  // Загрузка текстуры
  const loadTexture = (path: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = path;
    });
  };

  // Предзагрузка текстур при включении
  useEffect(() => {
    if (!showTextures) return;

    const texturesToLoad = Object.keys(skeleton)
      .map(boneName => getTexturePathForBone(boneName))
      .filter((path): path is string => path !== null);

    Promise.all(
      texturesToLoad.map(async (path) => {
        try {
          const img = await loadTexture(path);
          return [path, img] as const;
        } catch (error) {
          console.warn(`Failed to load texture: ${path}`, error);
          return null;
        }
      })
    ).then((results) => {
      const newMap = new Map<string, HTMLImageElement>();
      results.forEach((result) => {
        if (result) {
          newMap.set(result[0], result[1]);
        }
      });
      setLoadedTextures(newMap);
    });
  }, [showTextures, skeleton]);

  // Sync prop -> local state
  useEffect(() => {
    if (selectedBone !== undefined && selectedBone !== localSelectedBone) {
      setLocalSelectedBone(selectedBone);
    }
  }, [selectedBone]);

  // Calculate world transform for bone
  const calculateWorldTransform = (
    boneName: string,
    transforms: { [boneName: string]: WorldTransform } = {}
  ): WorldTransform => {
    const bone = skeleton[boneName];
    if (!bone) return { x: 0, y: 0, rotation: 0 };
    
    if (transforms[boneName]) {
      return transforms[boneName];
    }

    let worldTransform: WorldTransform = { x: bone.x, y: bone.y, rotation: bone.rotation };
    
    if (bone.parent) {
      const parentBone = skeleton[bone.parent];
      const parentTransform = calculateWorldTransform(bone.parent, transforms);

      // Determine child's local coords. If child was authored near the end of the parent
      // (in baseSkeleton) we treat it as anchored and compensate when parent.length changes.
      let localX = bone.x;
      let localY = bone.y;

      const AUTO_SNAP_ANCHORED_CHILDREN = true;
      const ANCHOR_DIST_THRESHOLD = 6; // px in base coordinates

      if (parentBone && AUTO_SNAP_ANCHORED_CHILDREN) {
        const baseChild = baseSkeleton[boneName];
        const baseParent = baseSkeleton[bone.parent];

        if (baseChild && baseParent && baseParent.length > 0) {
          const baseDeltaX = baseChild.x;
          const baseDeltaY = baseChild.y - baseParent.length;
          const dist = Math.sqrt(baseDeltaX * baseDeltaX + baseDeltaY * baseDeltaY);

          if (dist <= ANCHOR_DIST_THRESHOLD) {
            const deltaLen = parentBone.length - baseParent.length;
            
            // ИСКЛЮЧЕНИЕ для стоп: фиксируем Y, но разрешаем свободное движение по X
            const isFoot = boneName === 'leftFoot' || boneName === 'rightFoot';
            
            if (isFoot) {
              // Стопы: фиксируем Y (auto-snap), но X берем из текущей анимации
              localY = baseChild.y + deltaLen;
              localX = bone.x; // X из анимации
            } else {
              // Остальные кости: используем стандартную логику
              localY = baseChild.y + deltaLen;

              const APPLY_X_SHIFT = false; // disable lateral X shift; only apply Y compensation
              if (APPLY_X_SHIFT) {
                const sign = baseChild.x >= 0 ? 1 : -1;
                localX = baseChild.x + sign * Math.abs(deltaLen);
              } else {
                localX = baseChild.x;
              }
            }
          }
        }

        // Special case: torso width changes affect shoulders and legs differently
        if (bone.parent === 'torso') {
          const baseChild = baseSkeleton[boneName];
          const baseParent = baseSkeleton.torso;
          if (baseChild && baseParent && parentBone) {
            const deltaWidth = parentBone.width - baseParent.width;
            
            // For shoulders: adjust X based on side (left -, right +)
            if (boneName === 'leftShoulder') {
              localX = baseChild.x - deltaWidth / 2;
            } else if (boneName === 'rightShoulder') {
              localX = baseChild.x + deltaWidth / 2;
            }
            
            // For legs: both move down by deltaLength (torso got longer/shorter)
            const deltaLength = parentBone.length - baseParent.length;
            if (boneName === 'leftThigh' || boneName === 'rightThigh') {
              localY = baseChild.y + deltaLength;
            }
          }
        }
      }

      const cos = Math.cos(parentTransform.rotation);
      const sin = Math.sin(parentTransform.rotation);
      const rotatedX = localX * cos - localY * sin;
      const rotatedY = localX * sin + localY * cos;

      worldTransform.x = parentTransform.x + rotatedX;
      worldTransform.y = parentTransform.y + rotatedY;
      worldTransform.rotation = parentTransform.rotation + bone.rotation;
    }
    
    transforms[boneName] = worldTransform;
    return worldTransform;
  };

  // Draw bone
  const drawBone = (
    ctx: CanvasRenderingContext2D,
    transform: WorldTransform,
    bone: typeof skeleton[string],
    boneName: string,
    isSelected: boolean,
    opacity: number = 1.0
  ) => {
    ctx.save();
    ctx.translate(transform.x, transform.y);
    ctx.rotate(transform.rotation);
    // Choose color by bone group
    const groupColorMap: Record<string, string> = {
      head: '#ffd700',
      torso: '#87ceeb',
      leftShoulder: '#90ee90',
      leftForearm: '#32cd32',
      leftHand: '#2e8b57',
      rightShoulder: '#ffb6c1',
      rightForearm: '#ff69b4',
      rightHand: '#c71585',
      leftThigh: '#dda0dd',
      leftShin: '#ee82ee',
      leftFoot: '#da70d6',
      rightThigh: '#f4a460',
      rightShin: '#cd853f',
      rightFoot: '#8b4513',
      weapon: '#999999'
    };
    const defaultColor = '#ffd3c0';
    const color = isSelected ? '#ff6b6b' : (groupColorMap[boneName] || defaultColor);

    ctx.globalAlpha = opacity;

    // Рисуем текстуру если включен режим текстур
    if (showTextures) {
      const texturePath = getTexturePathForBone(boneName);
      const texture = texturePath ? loadedTextures.get(texturePath) : null;
      
      if (texture) {
        // Рисуем текстуру аналогично CharacterRenderer
        const boneWidth = bone.width;
        const boneLength = bone.length;
        
        ctx.save();
        ctx.translate(0, -5); // Смещение как в игре
        ctx.drawImage(
          texture,
          -boneWidth / 2,
          0,
          boneWidth,
          boneLength
        );
        ctx.restore();
      } else {
        // Fallback на цветные прямоугольники если текстура не загрузилась
        ctx.fillStyle = color;
        ctx.fillRect(-bone.width / 2, -5, bone.width, bone.length);
      }
    } else {
      // Обычный режим - цветные прямоугольники
      ctx.fillStyle = color;
      ctx.fillRect(-bone.width / 2, -5, bone.width, bone.length);
    }
    
  // Draw joint point
  ctx.fillStyle = isSelected ? '#ff0000' : '#222';
    ctx.fillRect(-3, -8, 6, 6);
    ctx.globalAlpha = 1.0;
    
    // Draw bone name
    if (showBoneNames) {
      ctx.fillStyle = '#000';
      ctx.font = '10px Arial';
      ctx.fillText(boneName, 5, -5);
    }

    // Draw rotation indicator for selected bone
    if (isSelected && opacity === 1.0) {
      ctx.strokeStyle = '#ff6b6b';
      ctx.lineWidth = 2;
      ctx.setLineDash([3, 3]);
      
      // Draw rotation circle around bone center
      ctx.beginPath();
      ctx.arc(0, 0, bone.length * 0.6, 0, Math.PI * 2);
      ctx.stroke();
      ctx.setLineDash([]);
      
      // Draw small arrow indicating rotation direction
      ctx.fillStyle = '#ff6b6b';
      ctx.beginPath();
      ctx.arc(bone.length * 0.6, 0, 3, 0, Math.PI * 2);
      ctx.fill();
    }
    
    ctx.restore();
  };

  // Draw skeleton
  const drawSkeleton = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Transform to center (без инверсии Y) и применяем зум
    ctx.save();
    ctx.translate(canvas.width / 2, canvas.height / 2);
    ctx.scale(zoom, zoom);  // Применяем зум
    // Убираем ctx.scale(1, -1); чтобы скелет был правильно ориентирован

    // Рисуем линию земли под скелетом
    // Земля находится условно на Y = 0 (на уровне torso origin)
    const groundY = 0;
    ctx.strokeStyle = 'rgba(100, 200, 100, 0.6)';
    ctx.lineWidth = 2 / Math.max(0.0001, zoom);
    ctx.beginPath();
    ctx.moveTo(-canvas.width / (2 * zoom), groundY);
    ctx.lineTo(canvas.width / (2 * zoom), groundY);
    ctx.stroke();

    // Рисуем предыдущий кадр слева с прозрачностью 70%
    if (previousSkeleton) {
      ctx.save();
      ctx.translate(-150, 0);
      renderSingleSkeleton(ctx, previousSkeleton, 0.3, false);
      ctx.restore();
    }

    // Рисуем следующий кадр справа с прозрачностью 70%
    if (nextSkeleton) {
      ctx.save();
      ctx.translate(150, 0);
      renderSingleSkeleton(ctx, nextSkeleton, 0.3, false);
      ctx.restore();
    }

    // Рисуем текущий кадр в центре
    renderSingleSkeleton(ctx, skeleton, 1.0, true);

    ctx.restore();
  };

  // Функция для рендеринга одного скелета
  const renderSingleSkeleton = (ctx: CanvasRenderingContext2D, skeletonToRender: Skeleton, opacity: number, showDebug: boolean) => {
    // Calculate world transforms for all bones
    const transforms: { [boneName: string]: WorldTransform } = {};
    // Determine rendering order: prefer provided drawOrder, otherwise fallback to known default order
    const defaultOrder = ['torso', 'head', 'leftShoulder', 'leftForearm', 'leftHand',
                          'rightShoulder', 'rightForearm', 'rightHand', 'leftThigh',
                          'leftShin', 'leftFoot', 'rightThigh', 'rightShin', 'rightFoot', 'weapon'];

    const desiredOrder = (drawOrder && drawOrder.length > 0) ? drawOrder : defaultOrder;

    // Ensure we draw only bones that exist in the skeleton and preserve any bones not listed
    const finalOrder: string[] = [];
    const included = new Set<string>();
    for (const bn of desiredOrder) {
      if (skeletonToRender[bn]) {
        finalOrder.push(bn);
        included.add(bn);
      }
    }
    // Append any bones missing from drawOrder so they are still rendered
    for (const bn of Object.keys(skeletonToRender)) {
      if (!included.has(bn)) finalOrder.push(bn);
    }

    // Подменяем skeleton на skeletonToRender для calculateWorldTransform
    const originalSkeleton = { ...skeleton };
    Object.assign(skeleton, skeletonToRender);

    // Draw bones in the final order
    finalOrder.forEach(boneName => {
      if (skeletonToRender[boneName]) {
        const transform = calculateWorldTransform(boneName, transforms);
        const isSelected = showDebug && localSelectedBone === boneName;
        drawBone(ctx, transform, skeletonToRender[boneName], boneName, isSelected, opacity);
      }
    });

    // Восстанавливаем оригинальный skeleton
    Object.assign(skeleton, originalSkeleton);

    // Debug overlay: draw connectors from parent end -> child origin to visualize gaps
    // This helps detect when children appear detached because frame changes lengths/positions.
    if (showDebug) {
      for (const boneName of Object.keys(transforms)) {
        const bone = skeletonToRender[boneName];
        const t = transforms[boneName];
        if (!bone || !t) continue;
        const parentName = bone.parent;
        if (!parentName) continue;
        const pt = transforms[parentName];
        if (!pt) continue;

        // Parent end in local parent coordinates is (0, parent.length)
        const parentLen = skeletonToRender[parentName]?.length || 0;
        const endX = pt.x + (-parentLen * Math.sin(pt.rotation));
        const endY = pt.y + (parentLen * Math.cos(pt.rotation));

        // Child origin is t.x, t.y
        const dx = t.x - endX;
        const dy = t.y - endY;
        const dist = Math.sqrt(dx*dx + dy*dy);

        ctx.beginPath();
        ctx.moveTo(endX, endY);
        ctx.lineTo(t.x, t.y);
        ctx.lineWidth = 1 / Math.max(0.0001, zoom); // keep thin regardless of zoom
        ctx.strokeStyle = dist > 1.5 ? 'rgba(255,80,80,0.9)' : 'rgba(120,200,120,0.6)';
        ctx.stroke();

        // draw small circles at points
        ctx.beginPath();
        ctx.fillStyle = dist > 1.5 ? '#ff5050' : '#78c878';
        ctx.arc(endX, endY, 3 / Math.max(0.0001, zoom), 0, Math.PI*2);
        ctx.fill();

        ctx.beginPath();
        ctx.fillStyle = dist > 1.5 ? '#ff8080' : '#bfe8bf';
        ctx.arc(t.x, t.y, 2.2 / Math.max(0.0001, zoom), 0, Math.PI*2);
        ctx.fill();
      }
    }
  };

  useEffect(() => {
    drawSkeleton();
  }, [skeleton, localSelectedBone, zoom, previousSkeleton, nextSkeleton]);

  // Convert canvas coordinates to world coordinates
  const canvasToWorld = (canvasX: number, canvasY: number) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    const x = (canvasX - rect.left - canvas.width / 2) / zoom;  // Учитываем зум
    const y = (canvasY - rect.top - canvas.height / 2) / zoom;  // Учитываем зум
    return { x, y };
  };

  // Find bone at position
  const findBoneAtPosition = (worldX: number, worldY: number): string | null => {
    const transforms: { [boneName: string]: WorldTransform } = {};
    
    for (const boneName in skeleton) {
      const bone = skeleton[boneName];
      const transform = calculateWorldTransform(boneName, transforms);
      
      // Simple hit test - check if point is within bone bounds
      const dx = worldX - transform.x;
      const dy = worldY - transform.y;
      
      // Rotate point back to bone local space
      const cos = Math.cos(-transform.rotation);
      const sin = Math.sin(-transform.rotation);
      const localX = dx * cos - dy * sin;
      const localY = dx * sin + dy * cos;
      
      if (localX >= -bone.width / 2 && localX <= bone.width / 2 &&
          localY >= -5 && localY <= bone.length - 5) {
        return boneName;
      }
    }
    
    return null;
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    const world = canvasToWorld(e.clientX, e.clientY);
    const bone = findBoneAtPosition(world.x, world.y);
    
    if (bone) {
      setLocalSelectedBone(bone);
      onSelectBone && onSelectBone(bone);
    } else {
      setLocalSelectedBone(null);
      onSelectBone && onSelectBone(null);
    }
  };

  const handleWheel = (e: React.WheelEvent) => {
    if (!localSelectedBone) return;
    
    e.preventDefault();
    
    // Создаем новый скелет
    const newSkeleton = { ...skeleton };
    const bone = newSkeleton[localSelectedBone];
    if (bone) {
      const currentAngle = bone.rotation || 0;
      // Колесико вверх = поворот против часовой, вниз = по часовой
      // Чувствительность: 0.05 радиан = ~3 градуса за один шаг колесика
      const rotationStep = 0.05;
      const deltaRotation = e.deltaY > 0 ? rotationStep : -rotationStep;
      
      const newAngle = currentAngle + deltaRotation;
      
      // Создаем новый объект кости
      newSkeleton[localSelectedBone] = { ...bone, rotation: newAngle };
      onSkeletonChange(newSkeleton);
    }
  };

  return (
    <div className="canvas-container">
      <canvas
        ref={canvasRef}
        width={1400}
        height={900}
        onMouseDown={handleMouseDown}
        onWheel={handleWheel}
        style={{ cursor: 'pointer' }}
      />
      {localSelectedBone && (
        <div style={{
          position: 'absolute',
          top: 10,
          left: 10,
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          Selected: {localSelectedBone}
        </div>
      )}
      <button
        onClick={() => setShowTextures(!showTextures)}
        style={{
          position: 'absolute',
          top: 10,
          right: 10,
          background: showTextures ? '#4CAF50' : '#666',
          color: 'white',
          border: 'none',
          padding: '10px 20px',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: 'bold',
          boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
          transition: 'background 0.3s'
        }}
      >
        {showTextures ? '🎨 Текстуры ВКЛ' : '🎨 Текстуры ВЫКЛ'}
      </button>
    </div>
  );
};

export default SkeletonCanvas;