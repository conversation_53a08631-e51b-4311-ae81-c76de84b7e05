import { useState, useRef, useEffect } from 'react';
import type { AnimationFrame, Skeleton } from '../types/skeleton';
import { baseSkeleton } from '../skeleton/baseSkeleton';
import LeftPanel from './LeftPanel';
import SkeletonCanvas from './SkeletonCanvas';
import RightPanel from './RightPanel';
import Timeline from './Timeline';


export interface AnimationData {
  name: string;
  frames: AnimationFrame[];
}

const AnimationEditor = () => {
  // Создаём безопасную глубокую копию базового скелета, чтобы
  // операции не мутировали оригинал и сброс работал корректно.
  const cloneDeep = <T,>(obj: T): T => JSON.parse(JSON.stringify(obj));

  const [currentSkeleton, setCurrentSkeleton] = useState<Skeleton>(() => cloneDeep(baseSkeleton));
  const [currentAnimation, setCurrentAnimation] = useState<AnimationData>({
    name: 'new_animation',
    frames: []
  });
  // -1 означает "нет выбранного кадра" (показывается базовая поза)
  const [currentFrameIndex, setCurrentFrameIndex] = useState(-1);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLooping, setIsLooping] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(400); // ms per frame
  const [zoom, setZoom] = useState(1.0); // Зум
  const [showBoneNames, setShowBoneNames] = useState(true); // Показывать названия костей
  // Выбранная кость (синхронизируется между Canvas и RightPanel)
  const defaultBone = Object.keys(baseSkeleton)[0] || null;
  const [selectedBone, setSelectedBone] = useState<string | null>(defaultBone);
  
  // Undo/Redo система
  const [undoHistory, setUndoHistory] = useState<Skeleton[]>([]);
  const [redoHistory, setRedoHistory] = useState<Skeleton[]>([]);
  const MAX_HISTORY = 30;

  const playbackRef = useRef<number | null>(null);

  // Playback logic
  useEffect(() => {
    if (isPlaying && currentAnimation.frames.length > 0) {
      playbackRef.current = setInterval(() => {
        setCurrentFrameIndex(prev => {
          const next = prev + 1;
          if (next >= currentAnimation.frames.length) {
            if (isLooping) {
              return 0;
            } else {
              setIsPlaying(false);
              return prev;
            }
          }
          return next;
        });
      }, playbackSpeed);
    } else {
      if (playbackRef.current) {
        clearInterval(playbackRef.current);
        playbackRef.current = null;
      }
    }

    return () => {
      if (playbackRef.current) {
        clearInterval(playbackRef.current);
      }
    };
  }, [isPlaying, isLooping, playbackSpeed, currentAnimation.frames.length]);

  // Apply current frame to skeleton (use deep clones to avoid accidental mutations)
  useEffect(() => {
    const frames = currentAnimation.frames;
    if (currentFrameIndex >= 0 && currentFrameIndex < frames.length) {
      const frame = frames[currentFrameIndex];
      // deep clone base skeleton
      const newSkeleton = cloneDeep(baseSkeleton);
      // Apply frame changes
      for (const boneName in frame) {
        if (newSkeleton[boneName]) {
          newSkeleton[boneName] = { ...newSkeleton[boneName], ...(frame as any)[boneName] };
        }
      }
      setCurrentSkeleton(newSkeleton);
    } else {
      setCurrentSkeleton(cloneDeep(baseSkeleton));
    }
  }, [currentFrameIndex, currentAnimation.frames]);

  // Вычисляем предыдущий и следующий скелеты
  const getPreviousSkeleton = (): Skeleton | null => {
    if (currentFrameIndex <= 0 || currentAnimation.frames.length === 0) return null;
    
    const prevFrame = currentAnimation.frames[currentFrameIndex - 1];
    const prevSkeleton = cloneDeep(baseSkeleton);
    
    for (const boneName in prevFrame) {
      if (prevSkeleton[boneName]) {
        prevSkeleton[boneName] = { ...prevSkeleton[boneName], ...(prevFrame as any)[boneName] };
      }
    }
    
    return prevSkeleton;
  };

  const getNextSkeleton = (): Skeleton | null => {
    if (currentFrameIndex === -1 || currentFrameIndex >= currentAnimation.frames.length - 1) return null;
    
    const nextFrame = currentAnimation.frames[currentFrameIndex + 1];
    const nextSkeleton = cloneDeep(baseSkeleton);
    
    for (const boneName in nextFrame) {
      if (nextSkeleton[boneName]) {
        nextSkeleton[boneName] = { ...nextSkeleton[boneName], ...(nextFrame as any)[boneName] };
      }
    }
    
    return nextSkeleton;
  };

  // Обработчик клавиш для Undo/Redo
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        handleUndo();
      } else if (e.ctrlKey && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
        e.preventDefault();
        handleRedo();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [undoHistory, redoHistory, currentSkeleton]);

  const handlePlay = () => {
    if (currentFrameIndex === -1 && currentAnimation.frames.length > 0) {
      setCurrentFrameIndex(0);
    }
    setIsPlaying(true);
  };
  const handlePause = () => setIsPlaying(false);
  const handleStop = () => {
    setIsPlaying(false);
    setCurrentFrameIndex(-1);
  };

  const handleFrameSelect = (frameIndex: number) => {
    setCurrentFrameIndex(frameIndex);
    setIsPlaying(false);
  };

  const handleCaptureFrame = () => {
    // Create frame from current skeleton differences from base
    const frame: AnimationFrame = {};
    
    // Сохраняем текущий порядок отрисовки вместо hardcoded
    const currentOrder = getCurrentDrawOrder();
    if (currentOrder && currentOrder.length > 0) {
      (frame as any).drawOrder = currentOrder.slice();
    }
    
    for (const boneName in currentSkeleton) {
      const currentBone = currentSkeleton[boneName];
      const baseBone = baseSkeleton[boneName];
      
      if (currentBone && baseBone) {
        const changes: Partial<typeof currentBone> = {};
        
        if (currentBone.x !== baseBone.x) changes.x = currentBone.x;
        if (currentBone.y !== baseBone.y) changes.y = currentBone.y;
        if (currentBone.rotation !== baseBone.rotation) changes.rotation = currentBone.rotation;
        if (currentBone.length !== baseBone.length) changes.length = currentBone.length;
        if (currentBone.width !== baseBone.width) changes.width = currentBone.width;
        
        if (Object.keys(changes).length > 0) {
          frame[boneName] = changes;
        }
      }
    }

    // Add frame to animation
    const newFrames = [...currentAnimation.frames];
    if (currentFrameIndex < newFrames.length) {
      newFrames[currentFrameIndex] = frame;
    } else {
      newFrames.push(frame);
    }

    setCurrentAnimation({
      ...currentAnimation,
      frames: newFrames
    });
  };

  const handleAddFrame = () => {
    const newFrames = [...currentAnimation.frames];
    newFrames.push({});
    setCurrentAnimation({
      ...currentAnimation,
      frames: newFrames
    });
    // Если ранее не было выбранного кадра (-1), переключаем на первый кадр
    if (currentFrameIndex === -1) {
      setCurrentFrameIndex(0);
    } else {
      setCurrentFrameIndex(newFrames.length - 1);
    }
  };

  const handleDuplicateFrame = () => {
    if (currentFrameIndex === -1 || currentAnimation.frames.length === 0) return;
    
    const newFrames = [...currentAnimation.frames];
    // Глубокое копирование текущего кадра
    const currentFrame = newFrames[currentFrameIndex];
    const duplicatedFrame = JSON.parse(JSON.stringify(currentFrame));
    
    // Вставляем дублированный кадр после текущего
    newFrames.splice(currentFrameIndex + 1, 0, duplicatedFrame);
    
    setCurrentAnimation({
      ...currentAnimation,
      frames: newFrames
    });
    
    // Переключаемся на дублированный кадр
    setCurrentFrameIndex(currentFrameIndex + 1);
  };

  const handleDeleteFrame = () => {
    if (currentFrameIndex === -1 || currentAnimation.frames.length === 0) return;
    
    const newFrames = [...currentAnimation.frames];
    newFrames.splice(currentFrameIndex, 1);
    
    setCurrentAnimation({
      ...currentAnimation,
      frames: newFrames
    });
    
    // Переключаемся на предыдущий кадр или на базовую позу если удалили все
    if (newFrames.length === 0) {
      setCurrentFrameIndex(-1);
    } else if (currentFrameIndex >= newFrames.length) {
      setCurrentFrameIndex(newFrames.length - 1);
    }
    // Если currentFrameIndex < newFrames.length, то остаёмся на том же индексе
  };

  const handleReorderFrames = (fromIndex: number, toIndex: number) => {
    if (fromIndex === toIndex) return;
    
    const newFrames = [...currentAnimation.frames];
    const [movedFrame] = newFrames.splice(fromIndex, 1);
    newFrames.splice(toIndex, 0, movedFrame);
    
    setCurrentAnimation({
      ...currentAnimation,
      frames: newFrames
    });
    
    // Обновляем индекс текущего кадра если он был перемещен
    if (currentFrameIndex === fromIndex) {
      setCurrentFrameIndex(toIndex);
    } else if (currentFrameIndex > fromIndex && currentFrameIndex <= toIndex) {
      setCurrentFrameIndex(currentFrameIndex - 1);
    } else if (currentFrameIndex < fromIndex && currentFrameIndex >= toIndex) {
      setCurrentFrameIndex(currentFrameIndex + 1);
    }
  };

  const handleLoadAnimation = (animationData: AnimationData) => {
    setCurrentAnimation(animationData);
    setCurrentFrameIndex(0);
    setIsPlaying(false);
  };

  const handleSaveAnimation = () => {
    // Сохраняем в формате игры: {"animation_name": [...frames]}
    const gameFormat = {
      [currentAnimation.name]: currentAnimation.frames
    };
    
    const dataStr = JSON.stringify(gameFormat, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${currentAnimation.name}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  // Функция для сохранения состояния в историю
  const saveToHistory = (skeleton: Skeleton) => {
    setUndoHistory(prev => {
      const newHistory = [...prev, cloneDeep(skeleton)];
      return newHistory.slice(-MAX_HISTORY); // Ограничиваем размер истории
    });
    setRedoHistory([]); // Очищаем redo при новом действии
  };

  // Undo функция
  const handleUndo = () => {
    if (undoHistory.length === 0) return;
    
    const previousState = undoHistory[undoHistory.length - 1];
    const newUndoHistory = undoHistory.slice(0, -1);
    
    setRedoHistory(prev => [...prev, cloneDeep(currentSkeleton)]);
    setUndoHistory(newUndoHistory);
    setCurrentSkeleton(cloneDeep(previousState));
  };

  // Redo функция
  const handleRedo = () => {
    if (redoHistory.length === 0) return;
    
    const nextState = redoHistory[redoHistory.length - 1];
    const newRedoHistory = redoHistory.slice(0, -1);
    
    setUndoHistory(prev => [...prev, cloneDeep(currentSkeleton)]);
    setRedoHistory(newRedoHistory);
    setCurrentSkeleton(cloneDeep(nextState));
  };

  const handleSkeletonChange = (newSkeleton: Skeleton) => {
    // Сохраняем текущее состояние в историю перед изменением
    saveToHistory(currentSkeleton);
    setCurrentSkeleton(newSkeleton);
  };

  const handleResetSkeleton = () => {
    saveToHistory(currentSkeleton);
    setCurrentSkeleton(cloneDeep(baseSkeleton));
  };

  // Draw order accessors for current frame (or base)
  const getCurrentDrawOrder = (): string[] => {
    if (currentFrameIndex >= 0 && currentFrameIndex < currentAnimation.frames.length) {
      const f = currentAnimation.frames[currentFrameIndex] as any;
      if (f.drawOrder && Array.isArray(f.drawOrder)) return f.drawOrder.slice();
    }
    // default order: keys of base skeleton
    return Object.keys(baseSkeleton);
  };

  const setCurrentDrawOrder = (newOrder: string[]) => {
    const newFrames = [...currentAnimation.frames];
    if (currentFrameIndex >= 0 && currentFrameIndex < newFrames.length) {
      const frame = { ...(newFrames[currentFrameIndex] || {}) } as any;
      frame.drawOrder = newOrder.slice();
      newFrames[currentFrameIndex] = frame;
      setCurrentAnimation({ ...currentAnimation, frames: newFrames });
    } else {
      // if no frame selected, we can set drawOrder on a pseudo-base frame by creating frame 0
      const frame = { ...(newFrames[0] || {}) } as any;
      frame.drawOrder = newOrder.slice();
      if (newFrames.length === 0) newFrames.push(frame);
      else newFrames[0] = frame;
      setCurrentAnimation({ ...currentAnimation, frames: newFrames });
      setCurrentFrameIndex(0);
    }
  };

  return (
    <div className="animation-editor">
      <LeftPanel
        animation={currentAnimation}
        onLoadAnimation={handleLoadAnimation}
        onSaveAnimation={handleSaveAnimation}
        playbackSpeed={playbackSpeed}
        onPlaybackSpeedChange={setPlaybackSpeed}
        onAnimationNameChange={(name: string) => setCurrentAnimation({ ...currentAnimation, name })}
        zoom={zoom}
        onZoomChange={setZoom}
        onUndo={handleUndo}
        onRedo={handleRedo}
        canUndo={undoHistory.length > 0}
        canRedo={redoHistory.length > 0}
        showBoneNames={showBoneNames}
        onShowBoneNamesChange={setShowBoneNames}
      />
      
      <SkeletonCanvas
        skeleton={currentSkeleton}
        onSkeletonChange={handleSkeletonChange}
        zoom={zoom}
        showBoneNames={showBoneNames}
        selectedBone={selectedBone}
        onSelectBone={(name: string | null) => setSelectedBone(name)}
        drawOrder={getCurrentDrawOrder()}
        previousSkeleton={getPreviousSkeleton()}
        nextSkeleton={getNextSkeleton()}
      />
      
      <RightPanel
        skeleton={currentSkeleton}
        onSkeletonChange={handleSkeletonChange}
        onCaptureFrame={handleCaptureFrame}
        onAddFrame={handleAddFrame}
        onResetSkeleton={handleResetSkeleton}
        currentFrameIndex={currentFrameIndex}
        totalFrames={currentAnimation.frames.length}
        selectedBone={selectedBone}
        onSelectBone={(name: string | null) => setSelectedBone(name)}
        drawOrder={getCurrentDrawOrder()}
        onSetDrawOrder={setCurrentDrawOrder}
      />
      
      <Timeline
        frames={currentAnimation.frames}
        currentFrameIndex={currentFrameIndex}
        isPlaying={isPlaying}
        isLooping={isLooping}
        onFrameSelect={handleFrameSelect}
        onPlay={handlePlay}
        onPause={handlePause}
        onStop={handleStop}
        onLoopToggle={() => setIsLooping(!isLooping)}
        onAddFrame={handleAddFrame}
        onDuplicateFrame={handleDuplicateFrame}
        onDeleteFrame={handleDeleteFrame}
        onReorderFrames={handleReorderFrames}
      />
    </div>
  );
};

export default AnimationEditor;