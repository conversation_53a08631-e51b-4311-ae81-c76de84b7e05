import { useState, useRef, useEffect } from 'react';
import { baseSkeleton } from '../skeleton/baseSkeleton';
import type { WorldTransform } from '../types/skeleton';

interface BoneRect {
  name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  worldX: number;
  worldY: number;
}

const TextureSlicer = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [imageOffset, setImageOffset] = useState({ x: 0, y: 0 });
  const [imageScale, setImageScale] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [selectedBones, setSelectedBones] = useState<string[]>([]);
  const [boneRects, setBoneRects] = useState<BoneRect[]>([]);
  const [showBoneNames, setShowBoneNames] = useState(false);
  const [skeletonScale, setSkeletonScale] = useState(2.4); // global skeleton scale (default 2x)
  const [skeletonOffsetX, setSkeletonOffsetX] = useState(-8);
  const [skeletonOffsetY, setSkeletonOffsetY] = useState(120);

  // Вычисление мировых координат кости с учетом иерархии (из SkeletonCanvas)
  const calculateWorldTransform = (
    boneName: string,
    transforms: { [boneName: string]: WorldTransform } = {}
  ): WorldTransform => {
    const bone = baseSkeleton[boneName];
    if (!bone) return { x: 0, y: 0, rotation: 0 };
    
    if (transforms[boneName]) {
      return transforms[boneName];
    }

    let worldTransform: WorldTransform = { x: bone.x, y: bone.y, rotation: bone.rotation };
    
    if (bone.parent) {
      const parentTransform = calculateWorldTransform(bone.parent, transforms);

      const cos = Math.cos(parentTransform.rotation);
      const sin = Math.sin(parentTransform.rotation);
      const rotatedX = bone.x * cos - bone.y * sin;
      const rotatedY = bone.x * sin + bone.y * cos;

      worldTransform.x = parentTransform.x + rotatedX;
      worldTransform.y = parentTransform.y + rotatedY;
      worldTransform.rotation = parentTransform.rotation + bone.rotation;
    }
    
    transforms[boneName] = worldTransform;
    return worldTransform;
  };

  // Создание прямоугольников для всех костей
  useEffect(() => {
    const rects: BoneRect[] = [];
    const transforms: { [boneName: string]: WorldTransform } = {};
    
    for (const boneName in baseSkeleton) {
      const bone = baseSkeleton[boneName];
      const worldTransform = calculateWorldTransform(boneName, transforms);
      
      rects.push({
        name: boneName,
        x: bone.x,
        y: bone.y,
        width: bone.width,
        height: bone.length,
        worldX: worldTransform.x,
        worldY: worldTransform.y,
      });
    }
    
    setBoneRects(rects);
  }, []);

  // Загрузка изображения
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const img = new Image();
      img.onload = () => {
        setImage(img);
        // Центрируем изображение
        if (canvasRef.current) {
          setImageOffset({
            x: canvasRef.current.width / 2 - img.width / 2,
            y: canvasRef.current.height / 2 - img.height / 2,
          });
        }
      };
      img.src = event.target?.result as string;
    };
    reader.readAsDataURL(file);
  };

  // Рисование на canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Очистка
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Рисуем изображение
    if (image) {
      ctx.save();
      ctx.translate(imageOffset.x, imageOffset.y);
      ctx.scale(imageScale, imageScale);
      ctx.drawImage(image, 0, 0);
      ctx.restore();
    }

  // Рисуем прямоугольники костей с глобальным масштабом и смещением
  const centerX = canvas.width / 2 + skeletonOffsetX;
  const centerY = canvas.height / 2 + skeletonOffsetY;
  const scale = skeletonScale; // глобальный масштаб скелета

    boneRects.forEach((rect) => {
      ctx.save();
      
      const screenX = centerX + rect.worldX * scale;
      const screenY = centerY + rect.worldY * scale;

      ctx.translate(screenX, screenY);

      // Подсветка выбранной кости (множественный выбор)
  const isSelected = selectedBones.includes(rect.name);
      if (isSelected) {
        ctx.strokeStyle = '#00ff00';
        ctx.lineWidth = 2;
      } else {
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
        ctx.lineWidth = 1;
      }

  // Рисуем прямоугольник кости с учётом глобального масштаба
  ctx.strokeRect(-rect.width * scale / 2, -5 * scale, rect.width * scale, rect.height * scale);

      // Название кости (опционально)
      if (showBoneNames) {
        ctx.fillStyle = isSelected ? '#00ff00' : '#ffffff';
        ctx.font = '10px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(rect.name, 5, -5);
      }

      ctx.restore();
    });
  }, [image, imageOffset, imageScale, boneRects, selectedBones, skeletonScale, skeletonOffsetX, skeletonOffsetY]);

  // Обработка колеса мыши (зум)
  const handleWheel = (e: React.WheelEvent<HTMLCanvasElement>) => {
    e.preventDefault();
    // Original step was ~10% per wheel step (0.9 / 1.1).
    // Reduce step 5x to make zoom smoother: ~2% per wheel step (0.98 / 1.02).
    const delta = e.deltaY > 0 ? 0.98 : 1.02;
    setImageScale((prev) => Math.max(0.1, Math.min(10, prev * delta)));
  };

  // Начало перетаскивания
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Проверяем клик по прямоугольнику кости (с учётом смещения и масштаба)
  const centerX = canvas.width / 2 + skeletonOffsetX;
  const centerY = canvas.height / 2 + skeletonOffsetY;
  const scale = skeletonScale; // глобальный масштаб скелета

    let clickedBone: string | null = null;
    
    for (const boneRect of boneRects) {
      const screenX = centerX + boneRect.worldX * scale;
      const screenY = centerY + boneRect.worldY * scale;
      
      // Проверка попадания с учетом масштаба
      const dx = x - screenX;
      const dy = y - screenY;
      
      if (dx >= -boneRect.width * scale / 2 && dx <= boneRect.width * scale / 2 &&
          dy >= -5 * scale && dy <= boneRect.height * scale - 5 * scale) {
        clickedBone = boneRect.name;
        break;
      }
    }

      if (clickedBone) {
      // toggle selection with Ctrl/Cmd, otherwise select only this bone
      if (e.ctrlKey || e.metaKey) {
        setSelectedBones(prev => prev.includes(clickedBone!) ? prev.filter(n => n !== clickedBone) : [...prev, clickedBone!]);
      } else {
        setSelectedBones([clickedBone]);
      }
    } else {
      // Начинаем перетаскивание изображения
      setIsDragging(true);
      setDragStart({ x: e.clientX - imageOffset.x, y: e.clientY - imageOffset.y });
    }
  };

  // Перетаскивание
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDragging) return;

    setImageOffset({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y,
    });
  };

  // Конец перетаскивания
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Маппинг названий костей на ID
  const boneIdMap: { [key: string]: number } = {
    head: 1,
    torso: 2,
    leftShoulder: 3,
    leftForearm: 4,
    leftHand: 5,
    rightShoulder: 6,
    rightForearm: 7,
    rightHand: 8,
    leftThigh: 9,
    leftShin: 10,
    leftFoot: 11,
    rightThigh: 12,
    rightShin: 13,
    rightFoot: 14,
  };

  // Обрезка выбранных костей (может быть несколько)
  const handleCrop = () => {
    if (selectedBones.length === 0 || !image || !canvasRef.current) return;
    const canvas = canvasRef.current;
  const centerX = canvas.width / 2 + skeletonOffsetX;
  const centerY = canvas.height / 2 + skeletonOffsetY;
  const scale = skeletonScale; // масштаб прямоугольников

    // Для каждой выбранной кости создаём отдельный файл и инициируем скачивание
    selectedBones.forEach((boneName) => {
      const rect = boneRects.find((r) => r.name === boneName);
      if (!rect) return;

      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = rect.width * 2; // сохраняем в увеличенном размере
      tempCanvas.height = rect.height * 2;
      const tempCtx = tempCanvas.getContext('2d');
      if (!tempCtx) return;

  const screenX = centerX + rect.worldX * scale;
  const screenY = centerY + rect.worldY * scale;

      const imgX = (screenX - rect.width * scale / 2 - imageOffset.x) / imageScale;
      const imgY = (screenY - 5 * scale - imageOffset.y) / imageScale;
      const imgWidth = (rect.width * scale) / imageScale;
      const imgHeight = (rect.height * scale) / imageScale;

      tempCtx.drawImage(
        image,
        imgX, imgY, imgWidth, imgHeight,
        0, 0, rect.width * 2, rect.height * 2
      );

  const boneId = boneIdMap[boneName] || boneName;
      tempCanvas.toBlob((blob) => {
        if (!blob) return;
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${boneId}.png`;
        link.click();
        URL.revokeObjectURL(url);
      });
    });
  };

  return (
    <div style={{ display: 'flex', height: '100vh', padding: '20px', gap: '10px' }}>
      {/* Левая панель со списком костей */}
      <div style={{
        width: '200px',
        background: '#1a1a1a',
        border: '2px solid #444',
        borderRadius: '4px',
        padding: '10px',
        overflowY: 'auto',
      }}>
        <h3 style={{ color: '#fff', margin: '0 0 10px 0', fontSize: '14px' }}>Выбор кости:</h3>
          <div style={{ display: 'flex', gap: '6px', marginBottom: '8px' }}>
            <button
              onClick={() => setSelectedBones(boneRects.map(b => b.name))}
              style={{ padding: '6px', fontSize: '12px', cursor: 'pointer' }}
            >
              Выбрать все
            </button>
            <button
              onClick={() => setSelectedBones([])}
              style={{ padding: '6px', fontSize: '12px', cursor: 'pointer' }}
            >
              Снять все
            </button>
          </div>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
          {boneRects.map((bone) => (
            <button
              key={bone.name}
              onClick={() => setSelectedBones(prev => prev.includes(bone.name) ? prev.filter(n => n !== bone.name) : [...prev, bone.name])}
              style={{
                padding: '8px',
                background: selectedBones.includes(bone.name) ? '#4a4a4a' : '#2a2a2a',
                color: selectedBones.includes(bone.name) ? '#00ff00' : '#fff',
                border: selectedBones.includes(bone.name) ? '1px solid #00ff00' : '1px solid #444',
                borderRadius: '3px',
                cursor: 'pointer',
                textAlign: 'left',
                fontSize: '12px',
                fontWeight: selectedBones.includes(bone.name) ? 'bold' : 'normal',
              }}
            >
              {bone.name}
            </button>
          ))}
        </div>
        <div style={{ marginTop: 12, display: 'flex', flexDirection: 'column', gap: 6 }}>
          <label style={{ color: '#ccc', fontSize: 12 }}>Масштаб скелета: {skeletonScale.toFixed(2)}x</label>
          <input
            type="range"
            min={0.5}
            max={4}
            step={0.1}
            value={skeletonScale}
            onChange={(e) => setSkeletonScale(Number(e.target.value))}
          />
          <label style={{ color: '#ccc', fontSize: 12 }}>Смещение X: {skeletonOffsetX}px</label>
          <input
            type="range"
            min={-600}
            max={600}
            step={1}
            value={skeletonOffsetX}
            onChange={(e) => setSkeletonOffsetX(Number(e.target.value))}
          />
          <label style={{ color: '#ccc', fontSize: 12 }}>Смещение Y: {skeletonOffsetY}px</label>
          <input
            type="range"
            min={-600}
            max={600}
            step={1}
            value={skeletonOffsetY}
            onChange={(e) => setSkeletonOffsetY(Number(e.target.value))}
          />
        </div>
      </div>

      {/* Основная область */}
      <div style={{ display: 'flex', flexDirection: 'column', flex: 1, gap: '10px' }}>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            style={{ display: 'none' }}
          />
          <button
            onClick={() => fileInputRef.current?.click()}
            style={{ padding: '10px 20px', cursor: 'pointer' }}
          >
            Загрузить изображение
          </button>
          
          <button
            onClick={handleCrop}
            disabled={selectedBones.length === 0 || !image}
            style={{
              padding: '10px 20px',
              cursor: selectedBones.length > 0 && image ? 'pointer' : 'not-allowed',
              opacity: selectedBones.length > 0 && image ? 1 : 0.5,
            }}
          >
            Обрезать {selectedBones.length > 0 ? `(${selectedBones.length})` : ''}
          </button>

          <label style={{ display: 'flex', alignItems: 'center', gap: '5px', color: '#fff', cursor: 'pointer' }}>
            <input
              type="checkbox"
              checked={showBoneNames}
              onChange={(e) => setShowBoneNames(e.target.checked)}
            />
            Показать названия костей
          </label>

          <div style={{ marginLeft: 'auto', color: '#fff' }}>
            Масштаб: {(imageScale * 100).toFixed(0)}%
          </div>
        </div>

        <canvas
          ref={canvasRef}
          width={1200}
          height={800}
          onWheel={handleWheel}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          style={{
            border: '2px solid #444',
            background: '#2a2a2a',
            cursor: isDragging ? 'grabbing' : 'grab',
            flex: 1,
          }}
        />

        <div style={{ color: '#fff', fontSize: '14px' }}>
          <p>
            <strong>Инструкция:</strong>
          </p>
          <ul style={{ margin: 0 }}>
            <li>Загрузите изображение персонажа</li>
            <li>Перемещайте изображение мышью (drag)</li>
            <li>Масштабируйте колесом мыши</li>
            <li>Выберите кость из списка слева или кликните на прямоугольник</li>
            <li>Нажмите "Обрезать" для сохранения текстуры кости</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default TextureSlicer;
