import { useState, useEffect, useRef } from 'react';
import type { Skeleton } from '../types/skeleton';
import { baseSkeleton } from '../skeleton/baseSkeleton';

interface RightPanelProps {
  skeleton: Skeleton;
  onSkeletonChange: (skeleton: Skeleton) => void;
  onCaptureFrame: () => void;
  onAddFrame: () => void;
  onResetSkeleton: () => void;
  currentFrameIndex: number;
  totalFrames: number;
  selectedBone?: string | null;
  onSelectBone?: (name: string | null) => void;
  drawOrder?: string[];
  onSetDrawOrder?: (newOrder: string[]) => void;
}

const RightPanel = ({
  skeleton,
  onSkeletonChange,
  onCaptureFrame,
  onAddFrame,
  onResetSkeleton,
  currentFrameIndex,
  totalFrames,
  selectedBone,
  onSelectBone,
  drawOrder,
  onSetDrawOrder
}: RightPanelProps) => {
  const [localSelectedBone, setLocalSelectedBone] = useState<string>(selectedBone || Object.keys(skeleton)[0] || 'torso');
  const dragOffsetRef = useRef({ x: 0, y: 0 });

  // Состояние для множественного выделения в списке порядка отрисовки
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [draggedItems, setDraggedItems] = useState<string[]>([]);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);

  // Sync external selection
  useEffect(() => {
    if (selectedBone && selectedBone !== localSelectedBone) setLocalSelectedBone(selectedBone);
  }, [selectedBone]);

  // If skeleton changes, ensure selected exists
  useEffect(() => {
    if (!localSelectedBone || !skeleton[localSelectedBone]) {
      const first = Object.keys(skeleton)[0] || 'torso';
      setLocalSelectedBone(first);
      onSelectBone && onSelectBone(first);
    }
  }, [skeleton]);

  const boneNames = Object.keys(skeleton);
  const currentBone = skeleton[localSelectedBone];
  const currentDrawOrder = (drawOrder && drawOrder.length > 0) ? drawOrder.slice() : Object.keys(skeleton);

  const handleBoneChange = (property: keyof typeof currentBone, value: number) => {
    if (!currentBone || !localSelectedBone) return;
    const newSkeleton = { ...skeleton };
    newSkeleton[localSelectedBone] = { ...currentBone, [property]: value };
    onSkeletonChange(newSkeleton);
  };

  const resetBone = () => handleBoneChange('rotation', 0);

  // drawOrder helpers
  const moveDrawOrder = (index: number, delta: number) => {
    const arr = (drawOrder && drawOrder.length > 0) ? drawOrder.slice() : Object.keys(skeleton);
    const newIndex = index + delta;
    if (newIndex < 0 || newIndex >= arr.length) return;
    const tmp = arr[newIndex];
    arr[newIndex] = arr[index];
    arr[index] = tmp;
    onSetDrawOrder && onSetDrawOrder(arr);
  };

  const resetDrawOrderToDefault = () => onSetDrawOrder && onSetDrawOrder(Object.keys(skeleton));

  // Обработчики для множественного выделения
  const handleItemClick = (boneName: string, e: React.MouseEvent) => {
    if (e.ctrlKey || e.metaKey) {
      // Ctrl+клик: добавить/убрать из выделения
      const newSelected = new Set(selectedItems);
      if (newSelected.has(boneName)) {
        newSelected.delete(boneName);
      } else {
        newSelected.add(boneName);
      }
      setSelectedItems(newSelected);
    } else if (e.shiftKey && selectedItems.size > 0) {
      // Shift+клик: выделить диапазон
      const lastSelected = Array.from(selectedItems)[selectedItems.size - 1];
      const lastIndex = currentDrawOrder.indexOf(lastSelected);
      const currentIndex = currentDrawOrder.indexOf(boneName);
      const start = Math.min(lastIndex, currentIndex);
      const end = Math.max(lastIndex, currentIndex);
      const newSelected = new Set(selectedItems);
      for (let i = start; i <= end; i++) {
        newSelected.add(currentDrawOrder[i]);
      }
      setSelectedItems(newSelected);
    } else {
      // Обычный клик: выделить только этот элемент
      setSelectedItems(new Set([boneName]));
    }
  };

  // Обработчики drag & drop
  const handleDragStart = (boneName: string, e: React.DragEvent) => {
    // Если элемент не выделен, выделяем только его
    const itemsToDrag = selectedItems.has(boneName)
      ? Array.from(selectedItems)
      : [boneName];

    setDraggedItems(itemsToDrag);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', JSON.stringify(itemsToDrag));
  };

  const handleDragOver = (index: number, e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (targetIndex: number, e: React.DragEvent) => {
    e.preventDefault();
    setDragOverIndex(null);

    if (draggedItems.length === 0) return;

    const newOrder = currentDrawOrder.slice();

    // Удаляем перетаскиваемые элементы из массива
    const itemsToMove = draggedItems.filter(item => newOrder.includes(item));
    const filteredOrder = newOrder.filter(item => !itemsToMove.includes(item));

    // Вычисляем новый индекс вставки с учетом удаленных элементов
    let insertIndex = targetIndex;
    for (const item of itemsToMove) {
      const originalIndex = newOrder.indexOf(item);
      if (originalIndex < targetIndex) {
        insertIndex--;
      }
    }

    // Вставляем элементы в новую позицию
    filteredOrder.splice(insertIndex, 0, ...itemsToMove);

    onSetDrawOrder && onSetDrawOrder(filteredOrder);
    setDraggedItems([]);
  };

  const handleDragEnd = () => {
    setDraggedItems([]);
    setDragOverIndex(null);
  };

  // Floating window (draggable)
  // For easier discovery during debugging, open floating window by default
  const [floatingOpen, setFloatingOpen] = useState(true);
  // Place initial position near top-left so it's unlikely to be off-screen
  const [floatPos, setFloatPos] = useState<{ x: number; y: number }>({ x: 20, y: 80 });
  const [isDraggingFloat, setIsDraggingFloat] = useState(false);

  const onFloatMouseDown = (e: React.MouseEvent) => {
    setIsDraggingFloat(true);
    dragOffsetRef.current.x = e.clientX - floatPos.x;
    dragOffsetRef.current.y = e.clientY - floatPos.y;
    e.stopPropagation();
  };

  useEffect(() => {
    const onMove = (ev: MouseEvent) => {
      if (!isDraggingFloat) return;
      setFloatPos({ x: ev.clientX - dragOffsetRef.current.x, y: ev.clientY - dragOffsetRef.current.y });
    };
    const onUp = () => setIsDraggingFloat(false);
    window.addEventListener('mousemove', onMove);
    window.addEventListener('mouseup', onUp);
    return () => {
      window.removeEventListener('mousemove', onMove);
      window.removeEventListener('mouseup', onUp);
    };
  }, [isDraggingFloat]);

  return (
    <div className="right-panel">
      <div className="panel-section">
        <h3>Управление кадрами</h3>
        <button className="btn primary" onClick={onCaptureFrame}>Запечатлить кадр</button>
        <button className="btn" onClick={onAddFrame} style={{ marginLeft: 8 }}>Добавить кадр</button>
        <button className="btn" onClick={onResetSkeleton} style={{ marginTop: 8, display: 'block' }}>Сбросить все кости</button>
        <div style={{ margin: '10px 0', fontSize: '12px', color: '#aaa' }}>
          {currentFrameIndex === -1 ? <>Базовая поза (кадры: {totalFrames})</> : <>Кадр {currentFrameIndex + 1} из {totalFrames || 1}</>}
        </div>
      </div>

      <div className="panel-section">
        <h3>Редактирование костей</h3>
        <div className="control-group">
          <label>Выбрать кость</label>
          <select value={localSelectedBone} onChange={(e) => { setLocalSelectedBone(e.target.value); onSelectBone && onSelectBone(e.target.value); }}>
            {boneNames.map(bn => <option key={bn} value={bn}>{bn}</option>)}
          </select>
        </div>

        {currentBone && (
          <>
            <div className="control-group">
              <label>X позиция (delta)</label>
              <input
                type="number"
                step="0.1"
                value={(() => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].x : 0;
                  return (currentBone.x - base).toFixed(1);
                })()}
                onChange={(e) => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].x : 0;
                  const abs = base + Number(e.target.value);
                  handleBoneChange('x', abs);
                }}
                style={{ width: '80px', marginRight: '8px' }}
              />
              <input
                type="range"
                min={-100}
                max={100}
                step={0.1}
                value={(() => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].x : 0;
                  return currentBone.x - base;
                })()}
                onChange={(e) => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].x : 0;
                  const abs = base + Number(e.target.value);
                  handleBoneChange('x', abs);
                }}
                style={{ flex: 1 }}
              />
            </div>

            <div className="control-group">
              <label>Y позиция (delta)</label>
              <input
                type="number"
                step="0.1"
                value={(() => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].y : 0;
                  return (currentBone.y - base).toFixed(1);
                })()}
                onChange={(e) => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].y : 0;
                  const abs = base + Number(e.target.value);
                  handleBoneChange('y', abs);
                }}
                style={{ width: '80px', marginRight: '8px' }}
              />
              <input
                type="range"
                min={-100}
                max={100}
                step={0.1}
                value={(() => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].y : 0;
                  return currentBone.y - base;
                })()}
                onChange={(e) => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].y : 0;
                  const abs = base + Number(e.target.value);
                  handleBoneChange('y', abs);
                }}
                style={{ flex: 1 }}
              />
            </div>

            <div className="control-group">
              <label>Поворот (delta, радианы)</label>
              <input
                type="number"
                step="0.01"
                value={(() => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].rotation : 0;
                  return (currentBone.rotation - base).toFixed(2);
                })()}
                onChange={(e) => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].rotation : 0;
                  const abs = base + Number(e.target.value);
                  handleBoneChange('rotation', abs);
                }}
              />
              <input
                type="range"
                min={-3.14}
                max={3.14}
                step={0.01}
                value={(() => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].rotation : 0;
                  return currentBone.rotation - base;
                })()}
                onChange={(e) => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].rotation : 0;
                  const abs = base + Number(e.target.value);
                  handleBoneChange('rotation', abs);
                }}
                style={{ marginTop: '5px' }}
              />
            </div>

            <div className="control-group">
              <label>Длина (delta)</label>
              <input
                type="number"
                step={1}
                value={(() => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].length : 0;
                  return (currentBone.length - base).toString();
                })()}
                onChange={(e) => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].length : 0;
                  const abs = base + Number(e.target.value);
                  handleBoneChange('length', abs);
                }}
                style={{ width: '80px', marginRight: '8px' }}
              />
              <input
                type="range"
                min={-50}
                max={50}
                step={1}
                value={(() => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].length : 0;
                  return currentBone.length - base;
                })()}
                onChange={(e) => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].length : 0;
                  const abs = base + Number(e.target.value);
                  handleBoneChange('length', abs);
                }}
                style={{ flex: 1 }}
              />
            </div>

            <div className="control-group">
              <label>Ширина (delta)</label>
              <input
                type="number"
                step={1}
                value={(() => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].width : 0;
                  return (currentBone.width - base).toString();
                })()}
                onChange={(e) => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].width : 0;
                  const abs = base + Number(e.target.value);
                  handleBoneChange('width', abs);
                }}
                style={{ width: '80px', marginRight: '8px' }}
              />
              <input
                type="range"
                min={-40}
                max={60}
                step={1}
                value={(() => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].width : 0;
                  return currentBone.width - base;
                })()}
                onChange={(e) => {
                  const base = baseSkeleton[localSelectedBone] ? baseSkeleton[localSelectedBone].width : 0;
                  const abs = base + Number(e.target.value);
                  handleBoneChange('width', abs);
                }}
                style={{ flex: 1 }}
              />
            </div>

            <button className="btn" onClick={resetBone}>Сбросить кость</button>
          </>
        )}
      </div>

      <div className="panel-section">
        <h3>Информация</h3>
        <div style={{ fontSize: '11px', color: '#999', lineHeight: '1.4' }}>
          <p>Родитель: {currentBone?.parent || 'нет'}</p>
          <p>Поворот в градусах: {currentBone ? (currentBone.rotation * 180 / Math.PI).toFixed(1) : 0}°</p>
        </div>
      </div>

      <div className="panel-section" style={{ padding: '6px 4px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h3 style={{ fontSize: '12px', margin: 0 }}>Порядок отрисовки</h3>
          <div>
            <button className="btn" style={{ padding: '6px', fontSize: '12px', marginRight: '6px' }} onClick={() => setFloatingOpen(true)}>Открыть</button>
            <button className="btn" style={{ padding: '6px', fontSize: '12px' }} onClick={resetDrawOrderToDefault}>Сброс</button>
          </div>
        </div>
      </div>

      {floatingOpen && (
        <div className="floating-draworder" style={{
          position: 'fixed',
          left: floatPos.x,
          top: floatPos.y,
          zIndex: 99999,
          width: 340,
          maxHeight: 'calc(100vh - 100px)',
          display: 'flex',
          flexDirection: 'column',
          boxShadow: '0 8px 26px rgba(0,0,0,0.7)',
          border: '2px solid #ffcc00',
          background: '#1e1e1e'
        }}>
          <div className="floating-header" onMouseDown={onFloatMouseDown} style={{
            cursor: 'move',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '6px 8px',
            background: '#222',
            color: '#fff',
            flexShrink: 0
          }}>
            <div style={{ fontSize: '13px' }}>Draw Order (Ctrl/Shift+клик, перетягивание)</div>
            <div style={{ display: 'flex', gap: '6px' }}>
              <button className="btn" style={{ padding: '4px 6px', fontSize: '12px' }} onClick={() => setFloatingOpen(false)}>✕</button>
            </div>
          </div>
          <div style={{
            padding: '8px',
            overflowY: 'auto',
            background: '#2b2b2b',
            flex: 1
          }}>
            {(currentDrawOrder || []).map((bn, idx) => {
              const isSelected = selectedItems.has(bn);
              const isDragging = draggedItems.includes(bn);
              const isDragOver = dragOverIndex === idx;

              return (
                <div
                  key={bn}
                  draggable
                  onDragStart={(e) => handleDragStart(bn, e)}
                  onDragOver={(e) => handleDragOver(idx, e)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(idx, e)}
                  onDragEnd={handleDragEnd}
                  onClick={(e) => handleItemClick(bn, e)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '6px 8px',
                    borderBottom: '1px solid rgba(255,255,255,0.03)',
                    background: isSelected ? 'rgba(0, 122, 204, 0.3)' : 'transparent',
                    opacity: isDragging ? 0.5 : 1,
                    borderTop: isDragOver ? '2px solid #ffcc00' : 'none',
                    cursor: 'grab',
                    userSelect: 'none',
                    transition: 'background 0.1s'
                  }}
                >
                  <div style={{ flex: 1, color: isSelected ? '#fff' : '#ddd' }}>{bn}</div>
                  <div style={{ display: 'flex', gap: '6px' }}>
                    <button
                      className="btn"
                      style={{ padding: '4px 6px', fontSize: '12px' }}
                      onClick={(e) => { e.stopPropagation(); moveDrawOrder(idx, -1); }}
                    >
                      ↑
                    </button>
                    <button
                      className="btn"
                      style={{ padding: '4px 6px', fontSize: '12px' }}
                      onClick={(e) => { e.stopPropagation(); moveDrawOrder(idx, 1); }}
                    >
                      ↓
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default RightPanel;