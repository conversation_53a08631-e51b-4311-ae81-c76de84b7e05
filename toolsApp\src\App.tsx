import { useState } from 'react';
import './App.css';
import AnimationEditor from './components/AnimationEditor';
import TextureSlicer from './components/TextureSlicer';

type Tab = 'animation' | 'textures';

function App() {
  const [activeTab, setActiveTab] = useState<Tab>('animation');

  return (
    <div className="app">
      <div style={{
        position: 'fixed',
        top: '10px',
        left: '10px',
        display: 'flex',
        gap: '10px',
        zIndex: 9999,
      }}>
        <button
          onClick={() => setActiveTab('animation')}
          style={{
            padding: '8px 15px',
            background: activeTab === 'animation' ? '#4a4a4a' : '#2a2a2a',
            color: '#fff',
            border: activeTab === 'animation' ? '2px solid #00ff00' : '1px solid #444',
            cursor: 'pointer',
            borderRadius: '4px',
            fontSize: '12px',
          }}
        >
          Animation
        </button>
        <button
          onClick={() => setActiveTab('textures')}
          style={{
            padding: '8px 15px',
            background: activeTab === 'textures' ? '#4a4a4a' : '#2a2a2a',
            color: '#fff',
            border: activeTab === 'textures' ? '2px solid #00ff00' : '1px solid #444',
            cursor: 'pointer',
            borderRadius: '4px',
            fontSize: '12px',
          }}
        >
          Textures
        </button>
      </div>

      {activeTab === 'animation' && <AnimationEditor />}
      {activeTab === 'textures' && <TextureSlicer />}
    </div>
  );
}

export default App;
